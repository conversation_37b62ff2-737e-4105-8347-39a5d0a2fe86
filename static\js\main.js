// 全局变量
let sessionId = generateSessionId();
let localChatHistory = [];
let currentPollingInterval = null;
let currentProgressInterval = null;
let isPolling = false;

// 通用的401错误处理函数
function handleUnauthorized() {
  localStorage.removeItem('user_token');
  localStorage.removeItem('user_info');
  window.location.href = '/login';
}

// 用户认证相关函数
async function checkLoginStatus() {
  const token = localStorage.getItem('user_token');
  const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');

  if (!token || !userInfo.username) {
    // 未登录，跳转到登录页面
    window.location.href = '/login';
    return;
  }

  // 验证token是否有效
  try {
    const response = await fetch('/api/verify-token', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 401) {
      // token无效或过期，处理未授权错误
      handleUnauthorized();
      return;
    }

    if (!response.ok) {
      throw new Error('验证失败');
    }

    // token有效，显示用户信息
    displayUserInfo(userInfo);
  } catch (error) {
    console.error('Token验证失败:', error);
    // 验证失败，处理未授权错误
    handleUnauthorized();
  }
}

function displayUserInfo(userInfo) {
  const usernameDisplay = document.getElementById('username-display');
  const userRole = document.getElementById('user-role');
  const avatarText = document.getElementById('avatar-text');
  const userAvatar = document.getElementById('user-avatar');
  const adminBtn = document.getElementById('admin-btn');

  // 下拉菜单中的元素
  const dropdownUsername = document.getElementById('dropdown-username');
  const dropdownRole = document.getElementById('dropdown-role');
  const dropdownAvatarText = document.getElementById('dropdown-avatar-text');
  const dropdownAvatar = document.querySelector('.dropdown-avatar');

  if (usernameDisplay) {
    usernameDisplay.textContent = userInfo.username;
  }

  if (dropdownUsername) {
    dropdownUsername.textContent = userInfo.username;
  }

  // 设置头像文字（用户名首字母）
  const firstLetter = userInfo.username.charAt(0).toUpperCase();
  if (avatarText) {
    avatarText.textContent = firstLetter;
  }
  if (dropdownAvatarText) {
    dropdownAvatarText.textContent = firstLetter;
  }

  // 设置角色信息和样式
  const roleText = userInfo.role === 'admin' ? '管理员' : '普通用户';
  const roleClass = userInfo.role === 'admin' ? 'admin' : 'user';

  if (userRole) {
    userRole.textContent = roleText;
    userRole.className = `user-role ${roleClass}`;
  }

  if (dropdownRole) {
    dropdownRole.textContent = roleText;
    dropdownRole.className = `dropdown-role ${roleClass}`;
  }

  // 设置头像颜色
  if (userAvatar) {
    userAvatar.className = `user-avatar ${roleClass}`;
  }
  if (dropdownAvatar) {
    dropdownAvatar.className = `dropdown-avatar ${roleClass}`;
  }

  // 如果是管理员，显示用户管理按钮
  if (userInfo.role === 'admin' && adminBtn) {
    adminBtn.style.display = 'flex';
  }
}

function logout() {
  if (confirm('确定要退出登录吗？')) {
    localStorage.removeItem('user_token');
    localStorage.removeItem('user_info');
    window.location.href = '/login';
  }
}

function goToAdminPanel() {
  window.location.href = '/admin/users';
}

// 下拉菜单控制函数
function toggleUserDropdown() {
  const userProfile = document.getElementById('user-profile');
  const userDropdown = document.getElementById('user-dropdown');

  if (userProfile && userDropdown) {
    const isOpen = userDropdown.classList.contains('show');
    if (isOpen) {
      closeUserDropdown();
    } else {
      openUserDropdown();
    }
  }
}

function openUserDropdown() {
  const userProfile = document.getElementById('user-profile');
  const userDropdown = document.getElementById('user-dropdown');

  if (userProfile && userDropdown) {
    userProfile.classList.add('active');
    userDropdown.classList.add('show');
  }
}

function closeUserDropdown() {
  const userProfile = document.getElementById('user-profile');
  const userDropdown = document.getElementById('user-dropdown');

  if (userProfile && userDropdown) {
    userProfile.classList.remove('active');
    userDropdown.classList.remove('show');
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async function() {
  // 检查登录状态
  await checkLoginStatus();

  // 初始化页面
  initPage();

  // 添加事件监听器
  addEventListeners();

  // 加载历史记录
  loadChatHistory();

  // 加载推荐问题
  loadRecommendedQuestions();

  // 加载部门列表
  loadDepartments();

  // 确保停止所有可能的轮询
  stopAllPolling();

  // 清除可能存在的本地存储
  localStorage.removeItem('pollingFolderName');
});

// 初始化页面
function initPage() {
  // 设置marked选项
  marked.setOptions({
    breaks: true,  // 支持GitHub风格的换行
    gfm: true      // 支持GitHub风格的Markdown
  });

  // 聚焦输入框
  const messageInput = document.getElementById('message-input');
  if (messageInput) {
    messageInput.focus();
  }
}

// 添加事件监听器
function addEventListeners() {
  // 发送按钮点击事件
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.addEventListener('click', sendMessage);
  }

  // 折叠/展开历史记录按钮点击事件
  const toggleHistoryBtn = document.getElementById('toggle-history-btn');
  const historyList = document.getElementById('history-list');
  const historyPagination = document.getElementById('history-pagination');
  if (toggleHistoryBtn && historyList && historyPagination) {
    toggleHistoryBtn.addEventListener('click', function() {
      // 切换折叠状态
      const isCollapsed = historyList.style.display === 'none';

      // 更新历史记录列表显示状态
      historyList.style.display = isCollapsed ? 'block' : 'none';

      // 同时更新分页控件的显示状态
      if (totalSessions && totalSessions.length > 0) {
        historyPagination.style.display = isCollapsed ? 'flex' : 'none';
      }

      // 更新按钮样式
      toggleHistoryBtn.classList.toggle('collapsed', !isCollapsed);

      // 保存折叠状态到本地存储
      localStorage.setItem('historyCollapsed', !isCollapsed ? 'true' : 'false');
    });

    // 加载时恢复保存的折叠状态
    const savedCollapsedState = localStorage.getItem('historyCollapsed');
    // 默认为折叠状态，除非明确设置为展开
    if (savedCollapsedState !== 'false') {
      historyList.style.display = 'none';
      historyPagination.style.display = 'none';
      toggleHistoryBtn.classList.add('collapsed');
      // 如果是首次加载，将折叠状态保存到本地存储
      if (savedCollapsedState === null) {
        localStorage.setItem('historyCollapsed', 'true');
      }
    }
  }

  // 输入框回车事件
  const messageInput = document.getElementById('message-input');
  if (messageInput) {
    messageInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
      }
    });

    // 自动调整输入框高度
    messageInput.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
  }

  // 新建聊天按钮点击事件
  const newChatBtn = document.getElementById('new-chat-btn');
  if (newChatBtn) {
    newChatBtn.addEventListener('click', startNewChat);
  }

  // 上传文档按钮点击事件
  const uploadBtn = document.getElementById('upload-btn');
  if (uploadBtn) {
    uploadBtn.addEventListener('click', showUploadDialog);
  }

  // 关闭上传对话框按钮点击事件
  const closeBtn = document.querySelector('.close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', hideUploadDialog);
  }

  // 上传表单提交事件
  const uploadForm = document.getElementById('upload-form');
  if (uploadForm) {
    uploadForm.addEventListener('submit', handleFormSubmit);
  }

  // 文件上传区域事件
  setupFileUploadArea();

  // 用户菜单事件
  const logoutBtn = document.getElementById('logout-btn');
  if (logoutBtn) {
    logoutBtn.addEventListener('click', logout);
  }

  const adminBtn = document.getElementById('admin-btn');
  if (adminBtn) {
    adminBtn.addEventListener('click', goToAdminPanel);
  }

  // 用户头像点击事件
  const userProfile = document.getElementById('user-profile');
  const userDropdown = document.getElementById('user-dropdown');
  if (userProfile && userDropdown) {
    userProfile.addEventListener('click', function(e) {
      e.stopPropagation();
      toggleUserDropdown();
    });
  }

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', function(e) {
    if (userDropdown && !userProfile.contains(e.target)) {
      closeUserDropdown();
    }
  });
}

// 设置文件上传区域
function setupFileUploadArea() {
  const dropArea = document.getElementById('drop-area');
  const fileInput = document.getElementById('file-upload');
  const fileInfo = document.getElementById('file-info');
  const fileName = document.getElementById('file-name');

  if (!dropArea || !fileInput || !fileInfo || !fileName) {
    return;
  }

  // 点击上传区域触发文件选择
  dropArea.addEventListener('click', () => {
    fileInput.click();
  });

  // 文件拖放处理
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, preventDefaults, false);
  });

  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  ['dragenter', 'dragover'].forEach(eventName => {
    dropArea.addEventListener(eventName, () => {
      dropArea.classList.add('dragover');
    });
  });

  ['dragleave', 'drop'].forEach(eventName => {
    dropArea.addEventListener(eventName, () => {
      dropArea.classList.remove('dragover');
    });
  });

  // 处理拖放的文件
  dropArea.addEventListener('drop', (e) => {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
      fileInput.files = files;
      updateFileInfo(files);
    }
  });

  // 处理选择的文件
  fileInput.addEventListener('change', () => {
    if (fileInput.files.length > 0) {
      updateFileInfo(fileInput.files);
    }
  });

  // 更新文件信息显示
  function updateFileInfo(files) {
    if (files.length === 1) {
      fileName.textContent = files[0].name;
    } else {
      fileName.textContent = `已选择 ${files.length} 个文件`;
    }
    fileInfo.style.display = 'flex';
  }
}

// 处理表单提交
async function handleFormSubmit(e) {
  e.preventDefault();

  const titleInput = document.getElementById('knowledge-title');
  const departmentInput = document.getElementById('department');
  const title = titleInput.value.trim();
  const department = departmentInput.value.trim();
  const files = document.getElementById('file-upload').files;

  if (!title) {
    alert('请输入知识标题');
    return;
  }

  if (!department) {
    alert('请选择或输入所属部门');
    return;
  }

  if (!files || files.length === 0) {
    alert('请选择要上传的文件');
    return;
  }

  // 显示上传状态
  const uploadForm = document.getElementById('upload-form');
  uploadForm.style.display = 'none';
  const uploadStatus = document.getElementById('upload-status');
  uploadStatus.style.display = 'block';
  uploadStatus.innerHTML = `
    <div class="spinner"></div>
    <div class="status-text">正在上传文档...</div>
    <div class="progress-container">
      <div class="progress-bar" id="upload-progress-bar"></div>
    </div>
  `;

  // 创建FormData对象
  const formData = new FormData();
  formData.append('title', title);
  formData.append('department', department);

  // 添加所有文件
  for (let i = 0; i < files.length; i++) {
    formData.append('files', files[i]);
  }

  try {
    // 发送上传请求
    const response = await fetch('/upload_documents/', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();

    if (result.status === 'success') {
      // 上传成功，开始轮询处理状态
      uploadStatus.innerHTML = `
        <div class="spinner"></div>
        <div class="status-text">文档上传成功，正在处理中...</div>
        <div class="progress-container">
          <div class="progress-bar" id="process-progress-bar" style="width: 10%;"></div>
        </div>
      `;

      // 开始轮询处理状态
      startPollingStatus(result.folder);
    } else {
      // 上传失败
      uploadStatus.innerHTML = `
        <div style="color: red; margin-bottom: 10px;">× 上传失败</div>
        <div>${result.message}</div>
        <button class="submit-btn" style="margin-top: 15px;" onclick="hideUploadDialog()">关闭</button>
      `;
    }
  } catch (error) {
    console.error('Error:', error);
    uploadStatus.innerHTML = `
      <div style="color: red; margin-bottom: 10px;">× 发生错误</div>
      <div>上传过程中发生错误，请重试</div>
      <button class="submit-btn" style="margin-top: 15px;" onclick="hideUploadDialog()">关闭</button>
    `;
  }
}

// 移除选择的文件
function removeFile() {
  const fileInput = document.getElementById('file-upload');
  const fileInfo = document.getElementById('file-info');

  if (fileInput && fileInfo) {
    fileInput.value = '';
    fileInfo.style.display = 'none';
  }
}

// 生成会话ID
function generateSessionId() {
  return 'session_' + Math.random().toString(36).substring(2, 15);
  //return 'session_12345' ;
}

// 发送消息
async function sendMessage() {
  const messageInput = document.getElementById('message-input');
  const chatHistory = document.getElementById('chat-history');

  if (!messageInput || !chatHistory) {
    return;
  }

  const message = messageInput.value.trim();
  if (!message) {
    return;
  }

  // 添加用户消息
  addMessage(message, 'user');
  messageInput.value = '';
  messageInput.style.height = 'auto';

  // 创建助手消息容器
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message assistant-message';

  // 创建头像
  const avatarDiv = document.createElement('div');
  avatarDiv.className = 'message-avatar assistant-avatar';
  avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
  messageDiv.appendChild(avatarDiv);

  // 创建消息内容区域
  const contentContainer = document.createElement('div');
  contentContainer.className = 'message-content-container';

  // 创建思考过程区域
  const thinkingDiv = document.createElement('div');
  thinkingDiv.className = 'thinking-process collapsible';
  thinkingDiv.innerHTML = `
    <div class="collapsible-header">
      <span>思考过程</span>
      <span class="toggle-button">›</span>
    </div>
    <div class="collapsible-content">
      <div class="thinking-content">思考中...</div>
    </div>
  `;

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  // 创建引用文档区域
  const sourceDiv = document.createElement('div');
  sourceDiv.className = 'source-documents collapsible';
  sourceDiv.innerHTML = `
    <div class="collapsible-header">
      <span>引用</span>
      <span class="toggle-button">›</span>
    </div>
    <div class="collapsible-content">
      <div class="source-content">无引用文档</div>
    </div>
  `;

  // 按顺序添加元素：思考过程 -> 消息内容 -> 引用文档
  contentContainer.appendChild(thinkingDiv);
  contentContainer.appendChild(messageContent);
  contentContainer.appendChild(sourceDiv);

  messageDiv.appendChild(contentContainer);
  chatHistory.appendChild(messageDiv);

  // 添加事件监听器到所有折叠按钮
  messageDiv.querySelectorAll('.collapsible-header').forEach(header => {
    header.addEventListener('click', function() {
      const content = this.nextElementSibling;
      const toggleButton = this.querySelector('.toggle-button');
      const isExpanded = content.style.display === 'block';

      content.style.display = isExpanded ? 'none' : 'block';

      if (toggleButton) {
        if (isExpanded) {
          toggleButton.classList.remove('expanded');
          toggleButton.style.transform = 'rotate(0deg)';
        } else {
          toggleButton.classList.add('expanded');
          toggleButton.style.transform = 'rotate(90deg)';
        }
      }
    });
  });

  // 初始化为折叠状态
  messageDiv.querySelectorAll('.collapsible-content').forEach(content => {
    content.style.display = 'none';
  });

  // 记录开始时间
  const startTime = new Date();

  try {
    const response = await fetch('/run_workflow/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'session-id': sessionId,
        'userId': 'admin'
      },
      body: JSON.stringify({ query: message }),
    });

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let accumulatedText = '';
    let mainResponse = '';

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      const text = decoder.decode(value, { stream: true });
      accumulatedText += text;

      // 检查是否包含思考过程
      const thinkMatch = accumulatedText.match(/<think>([\s\S]*?)<\/think>/);
      if (thinkMatch) {
        const thinking = thinkMatch[1].trim();
        // 更新思考过程内容，但不显示
        thinkingDiv.querySelector('.thinking-content').innerHTML = marked.parse(thinking);
        // 确保完全移除思考过程标记及其内容
        accumulatedText = accumulatedText.replace(/<think>[\s\S]*?<\/think>/, '').trim();
      }

      // 检查是否包含源文档
      const sourceMatch = accumulatedText.match(/<source>([\s\S]*?)<\/source>/);
      if (sourceMatch) {
        const sources = sourceMatch[1].trim();
        sourceDiv.querySelector('.source-content').innerHTML = marked.parse(sources);
        // 确保完全移除源文档标记及其内容
        accumulatedText = accumulatedText.replace(/<source>[\s\S]*?<\/source>/, '').trim();
      }

      // 更新主要响应内容
      mainResponse = accumulatedText;
      messageContent.innerHTML = marked.parse(mainResponse);

      // 自动滚动到底部
      chatHistory.scrollTop = chatHistory.scrollHeight;
    }

    // 计算思考时间
    const endTime = new Date();
    const thinkingTime = Math.round((endTime - startTime) / 1000);

    // 更新思考过程标题，显示思考时间
    const thinkingHeader = thinkingDiv.querySelector('.collapsible-header span:first-child');
    if (thinkingHeader) {
      thinkingHeader.textContent = `思考了 ${thinkingTime}s`;
    }

    // 更新本地对话历史
    localChatHistory.push({
      user: message,
      assistant: mainResponse
    });

    // 保存到数据库并获取消息ID
    const thinkingContent = thinkingDiv.querySelector('.thinking-content').innerHTML;
    const sourceContent = sourceDiv.querySelector('.source-content').innerHTML;
    const messageId = await saveChatToDatabase(message, mainResponse, thinkingContent, sourceContent);

    // 添加反馈按钮
    if (messageId) {
      addFeedbackButtons(messageDiv, messageId);
    }

    // 添加复制按钮
    const actions = document.createElement('div');
    actions.className = 'actions';
    actions.innerHTML = `
      <button class="action-button" onclick="copyMessage(this)"></button>
    `;
    contentContainer.appendChild(actions);

    // 自动朗读功能（如果在语音对话页面且启用了自动朗读）
    if (window.location.pathname.includes('voice_chat') &&
        document.getElementById('auto-tts-checkbox') &&
        document.getElementById('auto-tts-checkbox').checked) {

      // 延迟一点时间再开始朗读，让用户有时间看到回复
      setTimeout(() => {
        if (typeof speakText === 'function') {
          console.log('自动朗读AI回复:', mainResponse.substring(0, 50) + '...');
          speakText(mainResponse);
        }
      }, 1500);
    }

  } catch (error) {
    console.error('Error:', error);
    messageContent.textContent = '抱歉，发生了错误，请稍后重试。';
  }

  chatHistory.scrollTop = chatHistory.scrollHeight;
}

// 添加消息到聊天历史
function addMessage(content, type) {
  const chatHistory = document.getElementById('chat-history');
  if (!chatHistory) return;

  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${type}-message`;

  // 创建头像
  const avatarDiv = document.createElement('div');
  avatarDiv.className = `message-avatar ${type}-avatar`;

  if (type === 'user') {
    avatarDiv.innerHTML = '<i class="fas fa-user"></i>';
  } else {
    avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';
  }

  messageDiv.appendChild(avatarDiv);

  if (type === 'assistant') {
    // 创建消息内容容器
    const contentContainer = document.createElement('div');
    contentContainer.className = 'message-content-container';

    // 创建思考过程区域
    const thinkingDiv = document.createElement('div');
    thinkingDiv.className = 'thinking-process collapsible';
    thinkingDiv.innerHTML = `
      <div class="collapsible-header">
        <span>思考过程</span>
        <span class="toggle-button">›</span>
      </div>
      <div class="collapsible-content">
        <div class="thinking-content">无思考过程</div>
      </div>
    `;

    // 创建消息内容
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.innerHTML = marked.parse(content);

    // 创建引用文档区域
    const sourceDiv = document.createElement('div');
    sourceDiv.className = 'source-documents collapsible';
    sourceDiv.innerHTML = `
      <div class="collapsible-header">
        <span>引用</span>
        <span class="toggle-button">›</span>
      </div>
      <div class="collapsible-content">
        <div class="source-content">无引用文档</div>
      </div>
    `;

    // 按顺序添加元素
    contentContainer.appendChild(thinkingDiv);
    contentContainer.appendChild(messageContent);
    contentContainer.appendChild(sourceDiv);

    // 添加复制按钮
    const actions = document.createElement('div');
    actions.className = 'actions';
    actions.innerHTML = `
      <button class="action-button" onclick="copyMessage(this)"></button>
    `;
    contentContainer.appendChild(actions);

    messageDiv.appendChild(contentContainer);

    // 添加事件监听器到所有折叠按钮
    messageDiv.querySelectorAll('.collapsible-header').forEach(header => {
      header.addEventListener('click', function() {
        const content = this.nextElementSibling;
        const toggleButton = this.querySelector('.toggle-button');
        const isExpanded = content.style.display === 'block';

        content.style.display = isExpanded ? 'none' : 'block';

        if (toggleButton) {
          if (isExpanded) {
            toggleButton.classList.remove('expanded');
            toggleButton.style.transform = 'rotate(0deg)';
          } else {
            toggleButton.classList.add('expanded');
            toggleButton.style.transform = 'rotate(90deg)';
          }
        }
      });
    });

    // 初始化为折叠状态
    messageDiv.querySelectorAll('.collapsible-content').forEach(content => {
      content.style.display = 'none';
    });
  } else {
    // 用户消息，简单结构
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    messageDiv.appendChild(messageContent);
  }

  chatHistory.appendChild(messageDiv);
  chatHistory.scrollTop = chatHistory.scrollHeight;
}

// 复制消息
function copyMessage(button) {
  // 查找最近的消息内容元素
  let messageContent;

  // 首先尝试在message-content-container中查找
  const container = button.closest('.message-content-container');
  if (container) {
    messageContent = container.querySelector('.message-content');
  }

  // 如果没找到，尝试在整个消息中查找
  if (!messageContent) {
    const messageDiv = button.closest('.message');
    if (messageDiv) {
      messageContent = messageDiv.querySelector('.message-content');
    }
  }

  if (!messageContent) return;

  // 获取纯文本内容
  const textContent = messageContent.textContent;

  // 尝试使用clipboard API复制文本
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(textContent)
      .then(() => {
        showCopiedFeedback(button);
      })
      .catch(err => {
        console.error('Clipboard API失败:', err);
        // 尝试备用方法
        fallbackCopyTextToClipboard(textContent, button);
      });
  } else {
    // 使用备用方法
    fallbackCopyTextToClipboard(textContent, button);
  }
}

// 备用的复制方法
function fallbackCopyTextToClipboard(text, button) {
  const textArea = document.createElement('textarea');
  textArea.value = text;

  // 设置样式使其不可见
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);

  // 保存当前选中内容
  const selected = document.getSelection().rangeCount > 0 ?
    document.getSelection().getRangeAt(0) : false;

  // 选中文本并复制
  textArea.select();
  let success = false;
  try {
    success = document.execCommand('copy');
  } catch (err) {
    console.error('Fallback: 复制失败', err);
  }

  // 移除临时元素
  document.body.removeChild(textArea);

  // 恢复原来的选中内容
  if (selected) {
    document.getSelection().removeAllRanges();
    document.getSelection().addRange(selected);
  }

  if (success) {
    showCopiedFeedback(button);
  } else {
    alert('复制失败，请手动复制');
  }
}

// 显示复制成功的反馈
function showCopiedFeedback(button) {
  // 保存原始状态
  const originalContent = button.innerHTML;
  // 显示已复制文本
  button.innerHTML = '已复制';
  button.style.width = 'auto';

  // 2秒后恢复
  setTimeout(() => {
    button.innerHTML = originalContent;
    button.style.width = '';
  }, 2000);
}

// 开始新聊天
async function startNewChat() {
  // 清除前端显示
  const chatHistory = document.getElementById('chat-history');
  const messageInput = document.getElementById('message-input');

  if (chatHistory && messageInput) {
    chatHistory.innerHTML = '';
    messageInput.value = '';
    messageInput.focus();
  }

  // 保存当前会话到数据库
  if (localChatHistory.length > 0) {
    await saveSessionToDatabase();
  }

  // 清除本地历史
  localChatHistory = [];

  // 生成新的会话ID
  sessionId = generateSessionId();

  // 清除服务器端历史
  try {
    await fetch('/clear_history/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'session-id': sessionId
      }
    });
  } catch (error) {
    console.error('Error clearing history:', error);
  }
}

// 保存会话到数据库
async function saveSessionToDatabase() {
  if (localChatHistory.length === 0) return;

  try {
    const firstMessage = localChatHistory[0].user;
    const title = firstMessage.length > 30 ? firstMessage.substring(0, 30) + '...' : firstMessage;

    const token = localStorage.getItem('user_token');
    const response = await fetch('/save_session/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        session_id: sessionId,
        title: title,
        messages: localChatHistory
      })
    });

    if (response.status === 401) {
      handleUnauthorized();
      return;
    }

    const result = await response.json();
    if (result.status === 'success') {
      console.log('会话保存成功');
      // 刷新历史记录列表
      await loadChatHistory();

      // 如果是新会话，可能需要跳转到最新的一页
      const totalPages = Math.ceil(totalSessions.length / pageSize);
      if (totalPages > 0 && currentPage !== 1) {
        // 新会话通常会显示在第一页，所以跳转到第一页
        currentPage = 1;
        renderHistoryPage(currentPage);
        updatePaginationButtons();
      }
    } else {
      console.error('保存会话失败:', result.message);
    }
  } catch (error) {
    console.error('保存会话错误:', error);
  }
}

// 添加反馈按钮到消息
function addFeedbackButtons(messageDiv, messageId) {
  const contentContainer = messageDiv.querySelector('.message-content-container');
  if (!contentContainer) return;

  // 创建反馈区域
  const feedbackDiv = document.createElement('div');
  feedbackDiv.className = 'message-feedback';
  feedbackDiv.innerHTML = `
    <div class="feedback-question">这个回答对您有帮助吗？</div>
    <div class="feedback-buttons">
      <button class="feedback-btn thumbs-up" data-rating="1" title="有帮助">
        <i class="fas fa-thumbs-up"></i>
      </button>
      <button class="feedback-btn thumbs-down" data-rating="0" title="没有帮助">
        <i class="fas fa-thumbs-down"></i>
      </button>
    </div>
    <div class="feedback-comment" style="display: none;">
      <textarea placeholder="请告诉我们如何改进..." rows="2"></textarea>
      <button class="submit-feedback-btn">提交</button>
    </div>
  `;

  // 添加到消息内容后面
  contentContainer.appendChild(feedbackDiv);

  // 添加事件监听器
  const thumbsButtons = feedbackDiv.querySelectorAll('.feedback-btn');
  thumbsButtons.forEach(button => {
    button.addEventListener('click', function() {
      // 高亮选中的按钮
      thumbsButtons.forEach(btn => btn.classList.remove('selected'));
      this.classList.add('selected');

      // 显示评论框
      const commentDiv = feedbackDiv.querySelector('.feedback-comment');
      commentDiv.style.display = 'block';

      // 保存初步评分
      const rating = parseInt(this.dataset.rating);
      this.dataset.messageId = messageId;

      // 如果是负面评价，聚焦评论框
      if (rating === 0) {
        const textarea = commentDiv.querySelector('textarea');
        if (textarea) textarea.focus();
      }
    });
  });

  // 提交按钮事件
  const submitBtn = feedbackDiv.querySelector('.submit-feedback-btn');
  if (submitBtn) {
    submitBtn.addEventListener('click', function() {
      const selectedBtn = feedbackDiv.querySelector('.feedback-btn.selected');
      if (!selectedBtn) return;

      const rating = parseInt(selectedBtn.dataset.rating);
      const comment = feedbackDiv.querySelector('textarea').value;
      const messageId = selectedBtn.dataset.messageId;

      // 提交反馈
      submitFeedback(messageId, rating, comment, feedbackDiv);
    });
  }
}

// 提交反馈到服务器
async function submitFeedback(messageId, rating, comment, feedbackDiv) {
  try {
    const response = await fetch('/save_feedback/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message_id: messageId,
        rating: rating,
        comment: comment
      })
    });

    const result = await response.json();

    if (result.status === 'success') {
      // 显示感谢信息
      feedbackDiv.innerHTML = `
        <div class="feedback-thanks">
          感谢您的反馈！
        </div>
      `;
    } else {
      // 显示错误
      alert('提交反馈失败，请稍后重试');
    }
  } catch (error) {
    console.error('提交反馈错误:', error);
    alert('提交反馈时发生错误');
  }
}

// 保存单条对话到数据库
async function saveChatToDatabase(userMessage, assistantResponse, thinkingProcess, sourceDocuments) {
  try {
    const token = localStorage.getItem('user_token');
    const response = await fetch('/save_message/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        session_id: sessionId,
        user_message: userMessage,
        assistant_response: assistantResponse,
        thinking_process: thinkingProcess,
        source_documents: sourceDocuments
      })
    });

    if (response.status === 401) {
      handleUnauthorized();
      return null;
    }

    const result = await response.json();
    if (result.status === 'success') {
      return result.message_id;
    } else {
      console.error('保存消息失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('保存消息错误:', error);
    return null;
  }
}

// 全局分页变量
let currentPage = 1;
let pageSize = 10;
let totalSessions = [];

// 加载聊天历史
async function loadChatHistory() {
  const historyList = document.getElementById('history-list');
  const paginationDiv = document.getElementById('history-pagination');
  if (!historyList || !paginationDiv) return;

  try {
    const token = localStorage.getItem('user_token');
    const response = await fetch('/get_sessions/', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 401) {
      // 认证失败，处理未授权错误
      handleUnauthorized();
      return;
    }

    const sessions = await response.json();

    // 保存所有会话到全局变量
    totalSessions = sessions;

    // 如果没有历史记录，显示空消息并隐藏分页
    if (sessions.length === 0) {
      historyList.innerHTML = '';
      const emptyItem = document.createElement('div');
      emptyItem.className = 'history-item empty';
      emptyItem.textContent = '暂无历史记录';
      historyList.appendChild(emptyItem);
      paginationDiv.style.display = 'none';
      return;
    }

    // 显示分页控件，除非历史记录已折叠
    const savedCollapsedState = localStorage.getItem('historyCollapsed');
    paginationDiv.style.display = (savedCollapsedState === 'true') ? 'none' : 'flex';

    // 渲染当前页的历史记录
    renderHistoryPage(currentPage);

    // 设置分页按钮状态
    updatePaginationButtons();

    // 添加分页按钮事件
    setupPaginationEvents();
  } catch (error) {
    console.error('加载历史记录失败:', error);

    historyList.innerHTML = '';
    const errorItem = document.createElement('div');
    errorItem.className = 'history-item error';
    errorItem.textContent = '加载历史记录失败';
    historyList.appendChild(errorItem);
    paginationDiv.style.display = 'none';
  }
}

// 渲染指定页的历史记录
function renderHistoryPage(page) {
  const historyList = document.getElementById('history-list');
  if (!historyList) return;

  // 清空当前列表
  historyList.innerHTML = '';

  // 计算当前页的起始和结束索引
  const startIndex = (page - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalSessions.length);

  // 获取当前页的会话
  const currentPageSessions = totalSessions.slice(startIndex, endIndex);

  // 渲染当前页的会话
  currentPageSessions.forEach(session => {
    const historyItem = document.createElement('div');
    historyItem.className = 'history-item';
    historyItem.dataset.sessionId = session.session_id;

    historyItem.innerHTML = `
      <i class="fas fa-history"></i>
      <span>${session.title}</span>
      <button class="delete-history-btn" title="删除这条历史记录">
        <i class="fas fa-trash"></i>
      </button>
    `;

    // 点击历史记录项加载会话
    historyItem.addEventListener('click', () => loadSession(session.session_id));

    // 点击删除按钮删除会话
    const deleteBtn = historyItem.querySelector('.delete-history-btn');
    if (deleteBtn) {
      deleteBtn.addEventListener('click', (event) => {
        // 阻止事件冒泡，防止触发加载会话
        event.stopPropagation();
        // 确认删除
        if (confirm('确定要删除这条历史记录吗？')) {
          deleteSession(session.session_id);
        }
      });
    }

    historyList.appendChild(historyItem);
  });

  // 更新当前页码显示
  document.getElementById('current-page').textContent = page;
}

// 更新分页按钮状态
function updatePaginationButtons() {
  const prevBtn = document.getElementById('prev-page-btn');
  const nextBtn = document.getElementById('next-page-btn');

  if (!prevBtn || !nextBtn) return;

  // 计算总页数
  const totalPages = Math.ceil(totalSessions.length / pageSize);

  // 设置上一页按钮状态
  prevBtn.disabled = currentPage <= 1;

  // 设置下一页按钮状态
  nextBtn.disabled = currentPage >= totalPages;
}

// 设置分页按钮事件
function setupPaginationEvents() {
  const prevBtn = document.getElementById('prev-page-btn');
  const nextBtn = document.getElementById('next-page-btn');

  if (!prevBtn || !nextBtn) return;

  // 移除现有事件监听器，防止重复添加
  prevBtn.replaceWith(prevBtn.cloneNode(true));
  nextBtn.replaceWith(nextBtn.cloneNode(true));

  // 重新获取按钮引用
  const newPrevBtn = document.getElementById('prev-page-btn');
  const newNextBtn = document.getElementById('next-page-btn');

  // 添加上一页按钮事件
  newPrevBtn.addEventListener('click', () => {
    if (currentPage > 1) {
      currentPage--;
      renderHistoryPage(currentPage);
      updatePaginationButtons();
    }
  });

  // 添加下一页按钮事件
  newNextBtn.addEventListener('click', () => {
    const totalPages = Math.ceil(totalSessions.length / pageSize);
    if (currentPage < totalPages) {
      currentPage++;
      renderHistoryPage(currentPage);
      updatePaginationButtons();
    }
  });
}

// 加载特定会话
async function loadSession(sessionId) {
  try {
    // 保存当前会话
    if (localChatHistory.length > 0) {
      await saveSessionToDatabase();
    }

    // 设置新的会话ID
    this.sessionId = sessionId;

    // 清除本地历史
    localChatHistory = [];

    // 获取会话消息
    const response = await fetch(`/get_session_messages/${sessionId}`);
    const messages = await response.json();

    // 清除聊天历史
    const chatHistory = document.getElementById('chat-history');
    if (chatHistory) {
      chatHistory.innerHTML = '';
    }

    // 添加消息到界面
    messages.forEach(msg => {
      addMessage(msg.user_message, 'user');
      addMessage(msg.assistant_response, 'assistant');

      // 更新本地历史
      localChatHistory.push({
        user: msg.user_message,
        assistant: msg.assistant_response
      });
    });

    // 高亮当前会话
    document.querySelectorAll('.history-item').forEach(item => {
      item.classList.remove('active');
      if (item.dataset.sessionId === sessionId) {
        item.classList.add('active');
      }
    });

  } catch (error) {
    console.error('加载会话失败:', error);
  }
}

// 删除会话
async function deleteSession(sessionId) {
  try {
    // 调用删除会话的API
    const response = await fetch(`/delete_session/${sessionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.status === 'success') {
      // 如果删除成功，重新加载历史记录
      await loadChatHistory();

      // 如果当前页没有记录了，但还有其他页，则返回上一页
      const totalPages = Math.ceil(totalSessions.length / pageSize);
      if (currentPage > totalPages && totalPages > 0) {
        currentPage = totalPages;
        renderHistoryPage(currentPage);
        updatePaginationButtons();
      }

      // 如果当前正在查看的会话被删除，则开始新会话
      if (this.sessionId === sessionId) {
        startNewChat();
      }
    } else {
      alert('删除失败，请重试');
    }
  } catch (error) {
    console.error('删除会话错误:', error);
    alert('删除会话时发生错误');
  }
}

// 加载推荐问题
function loadRecommendedQuestions() {
  const recommendedQuestions = document.getElementById('recommended-questions');
  if (!recommendedQuestions) return;

  // 水利知识库相关问题
  // const questions = [
  //   '水库大坝的安全监测包括哪些方面？',
  //   '如何预防洪水灾害？',
  //   '水利工程建设的环境影响评估有哪些要点？',
  //   '水资源管理的主要挑战是什么？',
  //   '水利工程施工中的质量控制措施有哪些？',
  //   '水利工程的运行维护应注意什么？',
  //   '水利工程规划设计的基本原则是什么？',
  //   '水利工程中的生态保护措施有哪些？'
  // ];
  // 灾害应急知识库相关问题
  const questions = [
    '高层建筑火灾扑救的关键点是什么？',
    '消防指挥中心的主要职责有哪些？',
    '如何处理化学品泄漏引发的火灾？',
    '杭州暴雨事件5公里范围内的应急资源？',
    '发生了杭州暴雨事件，请你告诉我5公里内的应急资源？',
    '帮我查找下杭州暴雨事件详情。',
    '杭州暴雨事件启动预案。',
    '杭州暴雨事件终止预案。',
    '一张图查看防汛抗旱物资储备库。',
    '查询森林火灾预案。',
    '查询所有事件。',
    '预览摄像头138-Camera 01'
  ];


  const questionsContainer = document.createElement('div');

  questions.forEach(question => {
    const questionItem = document.createElement('div');
    questionItem.className = 'question-item';
    questionItem.textContent = question;

    questionItem.addEventListener('click', () => {
      const messageInput = document.getElementById('message-input');
      if (messageInput) {
        messageInput.value = question;
        messageInput.focus();
      }
    });

    questionsContainer.appendChild(questionItem);
  });

  recommendedQuestions.appendChild(questionsContainer);
}

// 显示上传对话框
function showUploadDialog() {
  const uploadDialog = document.getElementById('upload-dialog');
  if (uploadDialog) {
    uploadDialog.style.display = 'flex';

    // 重置表单状态
    const uploadForm = document.getElementById('upload-form');
    const uploadStatus = document.getElementById('upload-status');
    const fileInfo = document.getElementById('file-info');

    if (uploadForm && uploadStatus && fileInfo) {
      uploadForm.reset();
      uploadForm.style.display = 'block';
      uploadStatus.style.display = 'none';
      fileInfo.style.display = 'none';
    }
  }
}

// 隐藏上传对话框
function hideUploadDialog() {
  // 停止所有计时器
  stopAllPolling();

  // 清除本地存储
  localStorage.removeItem('pollingFolderName');

  // 隐藏对话框
  const uploadDialog = document.getElementById('upload-dialog');
  if (uploadDialog) {
    uploadDialog.style.display = 'none';
  }

  // 重置表单
  const uploadForm = document.getElementById('upload-form');
  const fileInfo = document.getElementById('file-info');
  const uploadStatus = document.getElementById('upload-status');

  if (uploadForm && fileInfo && uploadStatus) {
    uploadForm.reset();
    fileInfo.style.display = 'none';
    uploadStatus.style.display = 'none';
  }
}

// 开始轮询状态
async function startPollingStatus(folderName) {
  // 如果已经在轮询，先停止
  if (isPolling) {
    stopAllPolling();
  }

  // 保存当前轮询的文件夹名称
  localStorage.setItem('pollingFolderName', folderName);

  isPolling = true;
  const progressBar = document.getElementById('process-progress-bar');
  if (!progressBar) {
    console.error('进度条元素未找到');
    return;
  }

  let progress = 10;
  let completed = false;

  // 模拟进度增长
  currentProgressInterval = setInterval(() => {
    if (!completed && progress < 90) {
      progress += 5;
      progressBar.style.width = `${progress}%`;
    }
  }, 3000);

  // 使用setInterval代替while循环
  currentPollingInterval = setInterval(async () => {
    try {
      if (completed || !isPolling) {
        stopAllPolling();
        return;
      }

      console.log(`正在检查文件夹状态: ${folderName}`);
      const response = await fetch(`/process_status/${folderName}`);
      const result = await response.json();

      console.log("轮询状态结果:", result);

      const uploadStatus = document.getElementById('upload-status');
      if (!uploadStatus) {
        console.error('上传状态元素未找到');
        return;
      }

      if (result.status === 'completed') {
        // 清理计时器和本地存储
        stopAllPolling();
        localStorage.removeItem('pollingFolderName');
        completed = true;

        progressBar.style.width = '100%';

        uploadStatus.innerHTML = `
          <div style="color: green; margin-bottom: 10px;">✓ 文件处理完成</div>
          <div>文档已成功处理，现在可以开始提问</div>
          <button class="submit-btn" style="margin-top: 15px;" onclick="hideUploadDialog()">确定</button>
        `;

        console.log("处理完成，轮询已停止");
      } else if (result.status === 'failed') {
        // 清理计时器和本地存储
        stopAllPolling();
        localStorage.removeItem('pollingFolderName');
        completed = true;

        uploadStatus.innerHTML = `
          <div style="color: red; margin-bottom: 10px;">× 处理失败</div>
          <div>${result.message}</div>
          <button class="submit-btn" style="margin-top: 15px;" onclick="hideUploadDialog()">关闭</button>
        `;

        console.log("处理失败，轮询已停止");
      }
    } catch (error) {
      console.error('Error checking status:', error);
    }
  }, 3000);
}

// 停止所有轮询
function stopAllPolling() {
  if (currentPollingInterval) {
    clearInterval(currentPollingInterval);
    currentPollingInterval = null;
  }
  if (currentProgressInterval) {
    clearInterval(currentProgressInterval);
    currentProgressInterval = null;
  }
  isPolling = false;
  console.log("所有轮询已停止");
}

// 加载部门列表
async function loadDepartments() {
  try {
    const response = await fetch('/static/departments.json');
    const data = await response.json();

    if (data && data.departments && Array.isArray(data.departments)) {
      const departmentOptions = document.getElementById('department-options');
      if (departmentOptions) {
        // 清空现有选项
        departmentOptions.innerHTML = '';

        // 添加所有部门
        data.departments.forEach(department => {
          const option = document.createElement('option');
          option.value = department;
          departmentOptions.appendChild(option);
        });
      }
    }
  } catch (error) {
    console.error('加载部门列表失败:', error);
  }
}

// 导出全局函数
window.removeFile = removeFile;
window.copyMessage = copyMessage;
window.showUploadDialog = showUploadDialog;
window.hideUploadDialog = hideUploadDialog;