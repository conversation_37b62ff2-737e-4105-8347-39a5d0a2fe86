"""
主模块 - 包含函数调用代理核心逻辑
"""

import os
import re
import requests
import logging
from typing import Optional, Dict, Any

from .api_utils import load_api_config, get_api_url
from .agent_core import create_agent, create_function_calling_agent, format_agent_response, filter_think_tags
from .dynamic_tools_manager import dynamic_tools_manager
from .api_tools import (
    query_event_surround_resource,
    preview_camera, 
    one_map_position_resource,
    event_stop_plan,
    event_start_plan,
    query_all_event_infos,
    search_emergency_plan,
    query_area_plans,
    query_resource_cameras,
    start_call_by_name,
    query_model_resources,
    start_real_time_travel,
    start_meeting,
    end_meeting
)
from .param_extractors import (
    extract_event_params,
    extract_camera_params,
    extract_resource_params,
    extract_plan_params,
    extract_event_query_params,
    extract_area_params,
    extract_resource_camera_params,
    extract_call_params,
    extract_model_name,
    extract_real_time_travel,
    extract_meeting_name
)

# 创建logger
logger = logging.getLogger("langchain_agent")

def function_calling_agent(user_input: str, history=None, user_id="", session_id="") -> Optional[str]:
    """
    使用LangChain函数调用代理处理用户的输入
    """
    logger.info(f"函数调用代理接收到查询: {user_input}, 用户ID: {user_id}, 会话ID: {session_id}")

    # 设置当前用户ID和会话ID到API工具模块
    from .api_tools import set_user_id, set_session_id
    set_user_id(user_id)
    set_session_id(session_id)

    #首先检查用户配置的动态工具
    matched_tool = dynamic_tools_manager.check_tool_match(user_input)
    if matched_tool:
        logger.info(f"匹配到用户配置的工具: {matched_tool}")
        # 使用LLM代理处理动态工具调用
        return _handle_dynamic_tool_with_agent(user_input, matched_tool, history=history)

    # 判断是否是普通问答，是否需要调用API的查询
    # 使用关键词组合判断是否需要API查询
    keyword_combinations = [
        # 1. 查询事件周边资源
        ["事", "公里"],
        ["事", "周边"],
        ["事", "附近"],
        ["事", "km"],
        # 2. 预览摄像头
        ["览", "摄像头"],
        ["览", "监控"],
        ["览", "camera"],
        ["查", "摄像头"],
        ["打", "摄像头"],
        ["观", "摄像头"],
        ["打", "监控"],
        ["打", "视频"],
        ["查", "视频"],
        ["查", "监控"],
        ["启", "视频"],
        ["开", "监控"],
        # 3. 一张图查看资源位置
        ["一张图", "查"],
        ["一张图", "定"],
        # 4. 终止事件预案
        ["终", "预案"],
        ["停", "预案"],
        ["关", "预案"],
        # 5. 启动事件预案
        ["启", "预案"],
        ["开", "预案"],
        # 7. 查询事件信息
        ["查", "事件"],
        ["搜", "事件"],
        ["事", "列表"],
        # 8. 搜索应急预案
        ["查", "预案"],
        ["搜", "预案"],
        # 9. 查询区域预案
        ["市", "预案"],
        ["区", "预案"],
        # 10. 查询资源周边视频信息
        ["周边", "视频"],
        ["附近", "视频"],
        ["周边", "监控"],
        ["附近", "监控"],
        ["周边", "摄像头"],
        ["周围", "视频"],
        # 11. 发起呼叫
        ["找到", "发起"],
        ["呼叫", "队伍"],
        ["呼叫", "视频"],
        ["视频", "通话"],
        ["电话", "通话"],
        # 12. 查询模型周边资源
        ["模型", "资源"],
        ["模型", "附近"],
        ["模型", "周边"],
        # 13. 启动实时轨迹
        ["启动", "轨迹"],
        ["跟进", "轨迹"],
        ["调度", "轨迹"],
        # 14. 会议管理 - 修改为更符合实际使用场景的关键词组合
        ["开启", "会议"],
        ["启动", "会议"],
        ["开始", "会议"],
        ["创建", "会议"],
        ["建立", "会议"],
        ["结束", "会议"],
        ["终止", "会议"],
        ["关闭", "会议"],
        ["停止", "会议"]
    ]
    
    # 检查是否包含任何API关键词组合
    is_api_query = any(all(kw in user_input for kw in combination) for combination in keyword_combinations)
    
    # 输出调试信息
    logger.info(f"是否API查询: {is_api_query}")
    
    # 如果不是API查询，返回None，让主程序继续处理以实现流式输出
    if not is_api_query:
        logger.info("检测到普通问答请求，返回None以使用原有流程处理")
        return None
    
    # 1.检查是否是预案启动或终止请求
    is_plan_action_query = False
    detailed_api_result = None

    # 预案启动和终止关键词
    plan_start_keywords = ["启动", "开始", "执行", "开启"]
    plan_stop_keywords = ["终止", "停止", "结束", "关闭", "取消"]

    # 简化判断：只要包含动作词+预案就认为是预案操作
    if "预案" in user_input and (any(keyword in user_input for keyword in plan_start_keywords + plan_stop_keywords)):
        is_plan_action_query = True
    
        # 确定是启动还是终止预案
        is_start_plan = any(keyword in user_input for keyword in plan_start_keywords)
        action_type = "启动" if is_start_plan else "终止"
        
        # 尝试提取事件名称
        event_name = ""
        # 首先尝试匹配"启动XX事件预案"这种格式
        event_matches_between = re.search(r'(启动|开启|开始|执行|终止|停止|结束|取消|关闭)([^预案]+?事件)(?:的)?(?:应急)?预案', user_input)
        #event_matches_between = re.search(r'(启动|开启|开始|执行|终止|停止|结束|取消)([\w\s]+?事件)(?:的)?(?:应急)?预案', user_input)
        #event_matches_between = re.search(r'(启动|开启|开始|执行|终止|停止|结束|取消)([\w\s]+?)(?:的)?(?:应急)?预案', user_input)
        if event_matches_between:
            event_name = event_matches_between.group(2).strip()
            logger.info(f"提取到预案名称:: {event_name}")
        # 如果上面没匹配到，再尝试常规的"XX事件"模式，但排除包含"停止"、"终止"等词的匹配
        if not event_name:
            #event_matches = re.findall(r'([\w\s]+?事件)', user_input)
            event_matches = re.findall(r'([^启动开启开始执行终止停止结束取消关闭预案]+?事件)', user_input)
            if event_matches:
                for match in event_matches:
                    # 排除包含"停止"、"终止"等词的匹配
                    if not any(word in match for word in ["停止", "终止", "结束", "取消"]):
                        event_name = match
                        break
        # 如果能直接提取出事件名称，直接调用函数
        if event_name:
            logger.info(f"直接提取到事件名称: {event_name} 用于{action_type}预案")
            try:
                # 根据操作类型调用相应的函数
                if is_start_plan:
                    result = event_start_plan(f"{event_name},")
                else:
                    result = event_stop_plan(f"{event_name},")
                logger.info(f"成功通过规则提取事件名称并调用{action_type}预案API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用{action_type}预案API失败: {str(e)}")
                detailed_api_result = f"{action_type}预案失败: {str(e)}"
    
    # 2.检查是否是一张图查看资源位置请求
    is_resource_position_query = False
    
    # 一张图查看资源位置关键词
    resource_position_keywords = ["一张图查看", "一张图定位", "查看位置", "定位资源", "查询位置", "资源位置"]
    
    if any(keyword in user_input for keyword in resource_position_keywords):
        is_resource_position_query = True
        
        # 提取资源名称
        resource_params = extract_resource_params(user_input)
        resource_name = resource_params["resource_name"]
        
        # 如果能直接提取出资源名称，直接调用函数
        if resource_name:
            logger.info(f"直接提取到资源名称: {resource_name}")
            try:
                # 直接调用one_map_position_resource函数
                result = one_map_position_resource(resource_name)
                logger.info("成功通过规则提取资源名称并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用资源位置查询API失败: {str(e)}")
                detailed_api_result = f"资源位置查询失败: {str(e)}"
    
    # 3.检查是否是摄像头预览请求
    is_camera_query = False
    
    # 摄像头预览关键词
    camera_keywords = ["预览摄像头", "预览监控", "查看摄像头", "查看监控", "打开摄像头", "打开监控", 
                      "开启摄像头", "开启监控", "监控点", "camera", "摄像头预览", "监控预览", 
                      "摄像机", "监控点位", "录像", "视频画面"]
    
    if any(keyword in user_input.lower() for keyword in camera_keywords):
        is_camera_query = True
        
        # 提取摄像头ID
        camera_params = extract_camera_params(user_input)
        camera_id = camera_params["camera_id"]
        
        # 如果能直接提取出摄像头ID，直接调用函数
        if camera_id:
            logger.info(f"直接提取到摄像头ID: {camera_id}")
            try:
                # 直接调用preview_camera函数
                result = preview_camera(camera_id)
                logger.info("成功通过规则提取摄像头ID并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用预览摄像头API失败: {str(e)}")
                detailed_api_result = f"预览摄像头失败: {str(e)}"
    
    # 4.检查是否是应急预案查询或区域预案查询请求
    is_plan_query = False
    is_area_plan_query = False
    
    # 预案查询关键词
    plan_query_keywords = ["搜索应急预案", "搜索预案", "查询预案", "查找预案", "预案查询", "查询应急预案", 
                           "应急预案", "查找应急预案", "预案信息", "查看预案", "所有预案"]
    
    # 区域预案查询关键词
    area_plan_keywords = ["市预案", "区预案", "市应急预案", "区应急预案", "区域预案","市本级"]
    
    # 首先检查是否是区域预案查询（优先级更高）
    if (("市" in user_input or "区" in user_input) and "预案" in user_input) or any(keyword in user_input for keyword in area_plan_keywords):
        is_area_plan_query = True
        
        # 提取区域名称
        area_params = extract_area_params(user_input)
        area_name = area_params["area_name"]
        
        # 如果能提取出区域名称，直接调用函数
        if area_name:
            logger.info(f"直接提取到区域名称: {area_name}")
            try:
                # 直接调用query_area_plans函数
                result = query_area_plans(area_name)
                logger.info("成功通过规则提取区域名称并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用区域预案查询API失败: {str(e)}")
                detailed_api_result = f"区域预案查询失败: {str(e)}"
    # 如果不是区域预案查询，再检查是否是普通预案查询
    elif any(keyword in user_input for keyword in plan_query_keywords) or ("查询" in user_input and "预案" in user_input):
        is_plan_query = True
        
        # 提取预案类型
        plan_params = extract_plan_params(user_input)
        plan_type = plan_params["plan_type"]
        
        # 如果能提取出预案类型或者请求查询所有预案，直接调用函数
        if plan_type != None:
            logger.info(f"直接提取到预案类型: {plan_type}")
            try:
                # 直接调用search_emergency_plan函数
                result = search_emergency_plan(plan_type)
                logger.info("成功通过规则提取预案类型并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用搜索应急预案API失败: {str(e)}")
                detailed_api_result = f"搜索应急预案失败: {str(e)}"
    
    # 5.检查是否是资源周边视频查询请求
    is_resource_camera_query = False
    
    # 资源周边视频查询关键词
    resource_camera_keywords = ["周边视频", "周边监控", "附近视频", "附近监控", "周边摄像头", "附近摄像头",
                              "周围视频", "周围监控", "周围摄像头", "视频监控", "周边点位"]
    
    if any(keyword in user_input for keyword in resource_camera_keywords) or (("周边" in user_input or "附近" in user_input or "周围" in user_input) and ("视频" in user_input or "监控" in user_input or "摄像头" in user_input)):
        is_resource_camera_query = True
        
        # 提取资源名称和类型
        params = extract_resource_camera_params(user_input)
        resource_name = params["resource_name"]
        resource_type = params["resource_type"]
        
        # 如果能直接提取出资源名称和类型，直接调用函数
        if resource_name and resource_type:
            logger.info(f"直接提取到参数：资源名称={resource_name}, 资源类型={resource_type}")
            try:
                # 直接调用query_resource_cameras函数
                result = query_resource_cameras(f"{resource_name},{resource_type}")
                logger.info("成功通过规则提取资源周边视频参数并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用资源周边视频查询API失败: {str(e)}")
                detailed_api_result = f"资源周边视频查询失败: {str(e)}"
    
    # 6.检查是否是根据队伍/人名发起呼叫请求
    is_call_query = False
    
    # 呼叫相关关键词
    call_keywords = ["发起视频呼叫", "发起电话呼叫", "视频通话", "电话通话", "呼叫", "联系", "找到并呼叫", "找到并发起"]
    
    if any(keyword in user_input for keyword in call_keywords):
        is_call_query = True
        
        # 提取呼叫参数
        call_params = extract_call_params(user_input)
        resource_name = call_params["resource_name"]
        resource_type = call_params["resource_type"]
        calling_type = call_params["calling_type"]
        
        # 如果能提取出资源名称和资源类型，直接调用函数
        if resource_name and resource_type:
            logger.info(f"直接提取到呼叫参数：资源名称={resource_name}, 资源类型={resource_type}, 呼叫类型={calling_type}")
            try:
                # 直接调用start_call_by_name函数
                result = start_call_by_name(f"{resource_name},{resource_type},{calling_type}")
                logger.info("成功通过规则提取呼叫参数并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用发起呼叫API失败: {str(e)}")
                detailed_api_result = f"发起呼叫失败: {str(e)}"
    
    # 7.检查是否是按照模型搜索附近应急资源请求
    is_model_query = False
    
    # 模型搜索关键词
    model_keywords = ["模型附近", "模型周边", "模型周围", "按照模型", "根据模型", "使用模型搜索"]
    
    if any(keyword in user_input for keyword in model_keywords) or ("模型" in user_input and ("搜索" in user_input or "查询" in user_input)):
        is_model_query = True
        
        # 提取模型名称
        model_params = extract_model_name(user_input)
        model_name = model_params["model_name"]
        
        # 如果能提取出模型名称，直接调用函数
        if model_name:
            logger.info(f"直接提取到模型名称: {model_name}")
            try:
                # 直接调用query_model_resources函数
                result = query_model_resources(model_name)
                logger.info("成功通过规则提取模型名称并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用模型周边资源查询API失败: {str(e)}")
                detailed_api_result = f"模型周边资源查询失败: {str(e)}"
    
    # 8.检查是否是启动一键调度跟进实时轨迹请求
    is_travel_query = False
    
    # 实时轨迹关键词
    travel_keywords = ["启动一键调度", "跟进实时轨迹", "救援队伍轨迹", "实时跟踪", "实时跟进"]
    
    # 判断是否是实时轨迹请求
    travel_params = extract_real_time_travel(user_input)
    is_travel_request = travel_params["is_real_time_travel"]
    event_name = travel_params.get("event_name", "")
    
    if is_travel_request or any(keyword in user_input for keyword in travel_keywords):
        is_travel_query = True
        logger.info("检测到启动一键调度跟进实时轨迹请求")
        
        try:
            # 根据是否有事件名称调用不同的函数
            if event_name:
                result = start_real_time_travel(event_name)
                logger.info(f"成功调用指定事件实时轨迹API，事件名称: {event_name}，跳过LLM代理调用")
            else:
                result = start_real_time_travel()
                logger.info("成功调用全局实时轨迹API，跳过LLM代理调用")
            return result
        except Exception as e:
            logger.error(f"直接调用启动实时轨迹API失败: {str(e)}")
            detailed_api_result = f"启动实时轨迹失败: {str(e)}"
    
    
    # 9.检查是否是开启/结束会议请求
    is_meeting_query = False
    
    # 会议相关关键词 - 分开检查"开启"和"会议"这两个关键词
    start_keywords = ["开启", "启动", "开始", "创建", "建立"]
    end_keywords = ["结束", "终止", "关闭", "停止"]
    meeting_keyword = "会议"
    
    # 判断是开启还是结束会议 - 检查是否同时包含开启/结束关键词和会议关键词
    is_start_meeting = meeting_keyword in user_input and any(keyword in user_input for keyword in start_keywords)
    is_end_meeting = meeting_keyword in user_input and any(keyword in user_input for keyword in end_keywords)
    
    if is_start_meeting or is_end_meeting:
        is_meeting_query = True
        
        # 获取会议名称
        meeting_params = extract_meeting_name(user_input, is_start=is_start_meeting)
        meeting_name = meeting_params["meeting_name"]
        
        if meeting_name:
            logger.info(f"直接提取到会议名称: {meeting_name}")
            try:
                # 根据操作类型调用相应的函数
                if is_start_meeting:
                    result = start_meeting(meeting_name)
                    logger.info("成功通过规则提取会议名称并调用开启会议API，跳过LLM代理调用")
                else:
                    result = end_meeting(meeting_name)
                    logger.info("成功通过规则提取会议名称并调用结束会议API，跳过LLM代理调用")
                return result
            except Exception as e:
                action = "开启" if is_start_meeting else "结束"
                logger.error(f"直接调用{action}会议API失败: {str(e)}")
                detailed_api_result = f"{action}会议失败: {str(e)}"
    
    # 10.检查是否是查询事件信息请求
    is_event_info_query = False
    
    # 事件查询关键词
    event_info_keywords = ["查询事件", "搜索事件", "事件信息", "事件列表", "事件查询", "所有事件", "全部事件"]
    
    if any(keyword in user_input for keyword in event_info_keywords):
        is_event_info_query = True
        
        # 检查是否请求所有事件
        if "所有事件" in user_input or "全部事件" in user_input or user_input.strip() == "查询事件":
            logger.info("用户请求查询所有事件")
            query_param = "1,50,,,"
            logger.info(f"事件查询参数: {query_param}")
            try:
                # 直接调用query_all_event_infos函数
                result = query_all_event_infos(query_param)
                logger.info("成功通过规则提取事件查询参数并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用查询所有事件信息API失败: {str(e)}")
                detailed_api_result = f"查询事件信息失败: {str(e)}"
        else:
            # 提取事件查询参数
            event_params = extract_event_query_params(user_input)
            
            # 构建查询参数字符串
            query_param = f"{event_params['page_no'] if 'page_no' in event_params else event_params['page']},{event_params['page_size']},"
            query_param += f"{event_params['event_status']},{event_params['event_name']},{event_params['event_level']}"
            
            logger.info(f"事件查询参数: {query_param}")
            try:
                # 直接调用query_all_event_infos函数
                result = query_all_event_infos(query_param)
                logger.info("成功通过规则提取事件查询参数并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用查询所有事件信息API失败: {str(e)}")
                detailed_api_result = f"查询事件信息失败: {str(e)}"
    
    # 11.检查是否是事件周边资源查询请求
    is_resource_query = False
    
    # 尝试直接提取事件名称和距离，如果是简单格式的话
    resource_keywords = ["公里范围内的应急资源", "公里内的应急资源", "公里范围的应急资源", "公里的应急资源", "公里应急资源"]
    params = extract_event_params(user_input)
    extracted_type = params.get("resource_type", "")

    if (any(k in user_input for k in resource_keywords) or("公里" in user_input and "应急资源" in user_input) or extracted_type):
        is_resource_query = True

        # 提取事件名称和距离
    if is_resource_query:
        event_name = params["event_name"]
        distance = params["distance"]
        resource_tp = params.get("resource_type", "")  
            
        # 如果能直接提取出事件名称和距离，直接调用查询函数
        if event_name and distance:
            logger.info(f"直接提取到参数：事件名称={event_name}, 距离={distance}公里"
                        f"{'，资源类型='+resource_tp if resource_tp else ''}")
            try:
                # 直接调用API获取详细结果
                query_param = f"{event_name},{distance}"
                if resource_tp:                           # 例如 “仓库”
                    query_param += f",{resource_tp}"

                result = query_event_surround_resource(query_param)
                
                # 如果成功提取参数并得到结果，直接返回，不再调用LLM代理
                logger.info("成功通过规则提取参数并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用API函数失败: {str(e)}")
                detailed_api_result = f"API调用失败: {str(e)}"
    #detailed_api_result=False
    # 12.只有在直接参数提取失败的情况下才创建LLM代理
    if not detailed_api_result:
        logger.info("参数提取不完整或处理复杂查询，创建LLM代理")

        # 根据配置选择代理类型
        import configparser
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        function_calling_method = config.get("API", "FUNCTION_CALLING_METHOD", fallback="react").lower()

        # 创建代理
        if function_calling_method == "functioncalling":
            logger.info("使用Function Calling模式创建代理")
            agent = create_function_calling_agent()
        else:
            logger.info("使用ReAct模式创建代理")
            agent = create_agent()

        if not agent:
            logger.error("创建代理失败")
            return "系统错误：无法创建智能代理，请联系管理员"

        try:
            # 构建输入，包含历史对话
            agent_input = {"input": user_input}
            if history and len(history) > 0:
                # 构建历史对话文本
                history_text = ""
                for msg in history:
                    if msg.get("role") == "user":
                        history_text += f"用户: {msg.get('content', '')}\n"
                    elif msg.get("role") == "assistant":
                        history_text += f"助手: {msg.get('content', '')}\n"
                agent_input["chat_history"] = history_text

            # 使用代理处理查询
            response = agent.invoke(agent_input)
            logger.info("代理处理完成")
            
            # 获取最终输出
            if isinstance(response, dict) and "output" in response:
                agent_output = filter_think_tags(response["output"])  # 使用过滤器处理输出
                
                # 快速检测是否包含"未找到符合条件"类型的响应
                if "未找到符合条件的" in agent_output or "未找到" in agent_output and "事件" in agent_output:
                    logger.info("检测到空结果返回，直接返回")
                    return agent_output
            else:
                agent_output = filter_think_tags(str(response))  # 使用过滤器处理输出
            
            # 格式化结果
            formatted_response = format_agent_response(agent_output)
            return formatted_response
            
        except Exception as e:
            logger.error(f"代理处理错误: {str(e)}")
            return f"处理查询时出错: {str(e)}"
    
    # 如果有API结果但LLM处理失败，返回API结果
    return detailed_api_result


def _handle_dynamic_tool_with_agent(user_input: str, tool_name: str, history=None) -> Optional[str]:
    """使用代理处理动态工具调用"""
    try:
        logger.info(f"使用代理处理动态工具调用: {tool_name}")

        # 根据配置选择代理类型
        import configparser
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        function_calling_method = config.get("API", "FUNCTION_CALLING_METHOD", fallback="react").lower()

        # 创建包含动态工具的代理
        if function_calling_method == "functioncalling":
            logger.info("使用Function Calling模式处理动态工具")
            agent = create_dynamic_function_calling_agent()
        else:
            logger.info("使用ReAct模式处理动态工具")
            agent = create_dynamic_react_agent()

        if not agent:
            logger.error("创建动态工具代理失败")
            return "系统错误：无法创建智能代理，请联系管理员"

        try:
            # 构建输入，包含历史对话
            agent_input = {"input": user_input}
            if history and len(history) > 0:
                # 构建历史对话文本
                history_text = ""
                for msg in history:
                    if msg.get("role") == "user":
                        history_text += f"用户: {msg.get('content', '')}\n"
                    elif msg.get("role") == "assistant":
                        history_text += f"助手: {msg.get('content', '')}\n"
                agent_input["chat_history"] = history_text

            # 使用代理处理查询
            response = agent.invoke(agent_input)
            logger.info("动态工具代理处理完成")

            # 获取最终输出
            if isinstance(response, dict):
                output = response.get("output", str(response))
            else:
                output = str(response)

            # 格式化输出
            formatted_output = format_agent_response(output)
            logger.info(f"动态工具代理最终输出: {formatted_output}")

            return formatted_output

        except Exception as e:
            logger.error(f"动态工具代理处理失败: {str(e)}")
            return f"处理查询时出错: {str(e)}"

    except Exception as e:
        logger.error(f"处理动态工具调用失败: {str(e)}")
        return f"处理查询时出错: {str(e)}"


def create_dynamic_react_agent():
    """创建包含动态工具的ReAct代理"""
    try:
        # 这里可以扩展create_agent方法来包含动态工具
        # 暂时使用现有的create_agent，后续可以优化
        return create_agent()
    except Exception as e:
        logger.error(f"创建动态ReAct代理失败: {str(e)}")
        return None


def create_dynamic_function_calling_agent():
    """创建包含动态工具的Function Calling代理"""
    try:
        from .dynamic_agent_core import DynamicCustomFunctionCallingAgent, OutputFilterCallbackHandler

        # 加载配置
        import configparser
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')

        # 初始化LLM
        llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")
        model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b")

        if llm_type.lower() == "openai":
            from langchain_openai import ChatOpenAI
            openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback="")
            openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback="https://api.openai.com/v1")

            llm = ChatOpenAI(
                model_name=model_name,
                openai_api_key=openai_api_key,
                openai_api_base=openai_api_base,
            )
        else:
            from langchain_community.chat_models import ChatOllama
            ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")

            llm = ChatOllama(
                model=model_name,
                base_url=ollama_base_url
            )

        # 添加输出过滤回调
        output_filter = OutputFilterCallbackHandler()
        if not hasattr(llm, 'callbacks') or llm.callbacks is None:
            llm.callbacks = [output_filter]
        else:
            llm.callbacks.append(output_filter)

        # 创建动态Function Calling代理
        return DynamicCustomFunctionCallingAgent(llm, output_filter)
    except Exception as e:
        logger.error(f"创建动态Function Calling代理失败: {str(e)}")
        return None