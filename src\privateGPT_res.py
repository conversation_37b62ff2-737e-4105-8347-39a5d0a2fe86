#!/usr/bin/env python3
from langchain.chains import RetrievalQ<PERSON>
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.embeddings import OllamaEmbeddings
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain_community.llms import Ollama
from langchain_openai import ChatOpenAI  # 添加OpenAI支持
from langchain.prompts import PromptTemplate
import os
import argparse
import time
from typing import AsyncGenerator, List, Dict, Any
from langchain.callbacks import AsyncIteratorCallbackHandler
import asyncio
from pydantic import BaseModel
from dotenv import load_dotenv
import json
import configparser

# 导入函数调用模块
try:
    from src.agent import function_calling_agent
    ENABLE_FUNCTION_CALLING = True
except ImportError:
    ENABLE_FUNCTION_CALLING = False
    print("函数调用模块导入失败，函数调用功能已禁用")

# 从params.py合并的代码
class WorkflowParameters(BaseModel):
    youtube_video_id: str

# 从utils.py合并的代码
load_dotenv()
replacement_array = [(f"<{key}>", value) for key, value in os.environ.items()]

def replace_api_key(s):
    for old, new in replacement_array:
        s = s.replace(old, new)
    return s

def load_agent_specs(agent_spec_path: str):
    with open(agent_spec_path, 'r') as file:
        json_content = file.read()
    return json.loads(json_content)

# 加载配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
if os.path.exists(config_path):
    try:
        # 明确指定 UTF-8 编码
        config.read(config_path, encoding='utf-8')
    except Exception as e:
        print(f"使用 UTF-8 编码加载配置文件失败: {str(e)}")
        try:
            config.read(config_path, encoding='latin1')
        except Exception as e2:
            print(f"使用 latin1 编码加载配置文件失败: {str(e2)}")
else:
    try:
        # 明确指定 UTF-8 编码
        config.read("config.ini", encoding='utf-8')
    except Exception as e:
        print(f"使用 UTF-8 编码加载默认配置文件失败: {str(e)}")
        try:
            config.read("config.ini", encoding='latin1')
        except Exception as e2:
            print(f"使用 latin1 编码加载默认配置文件失败: {str(e2)}")

# 从配置文件获取设置
model_name = os.environ.get("MODEL", config.get('LLM', 'MODEL', fallback='deepseek-r1:32b'))
embeddings_model_name = os.environ.get("EMBEDDINGS_MODEL_NAME", config.get('EMBEDDINGS', 'MODEL_NAME', fallback='Private_GPT/sentence-transformers/bge-large-zh-v1.5'))
embedding_type = os.environ.get("EMBEDDING_TYPE", config.get('EMBEDDINGS', 'EMBEDDING_TYPE', fallback='huggingface'))
base_url = os.environ.get("BASE_URL", config.get('EMBEDDINGS', 'BASE_URL', fallback='http://localhost:11434'))
ollama_model = os.environ.get("OLLAMA_MODEL", config.get('EMBEDDINGS', 'OLLAMA_MODEL', fallback='dztech/bge-large-zh:v1.5'))
persist_directory = os.environ.get("PERSIST_DIRECTORY", config.get('DATABASE', 'PERSIST_DIRECTORY', fallback='Private_GPT/db_DOC_basic_600'))
target_source_chunks = int(os.environ.get('TARGET_SOURCE_CHUNKS', config.get('LLM', 'TARGET_SOURCE_CHUNKS', fallback='5')))
vector_db_type = os.environ.get("VECTOR_DB_TYPE", config.get('DATABASE', 'VECTOR_DB_TYPE', fallback='qdrant'))
qdrant_host = os.environ.get("QDRANT_HOST", config.get('QDRANT', 'HOST', fallback='localhost'))
qdrant_port = int(os.environ.get("QDRANT_PORT", config.get('QDRANT', 'PORT', fallback='7541')))

# 获取相似度阈值
score_threshold = config.getfloat("DATABASE", "SCORE_THRESHOLD", fallback=0.7)

# 修改模板定义部分，将其放在函数外部
# template = """
# 已知信息：{context}
# 历史对话：{chat_history}
# 问题：{question}
# You work in Chinese language, so give answer and search information in Chinese.\
# IF you can not get the correct answer, just say you don't know.\
# If you get good sentences for answer, summary the answer in Chinese.\
# If your answer is not from local sentenses searched, say "这个答案来自于大语言模型".
# """

#from constants import CHROMA_SETTINGS

template="""
    你是一个专业的知识问答助手，能够精确理解用户的问题并提供专业回答。

    **参考信息**：
    {context}

    **用户问题**：
    {question}

    **回答要求**：
    1. 请仔细理解用户问题的核心意图，即使问题很长也要抓住关键点。
    2. 优先使用参考信息回答，如果参考信息不足，可以使用你的知识补充。
    3. 回答要清晰、全面、有条理，不要答非所问。
    4. 使用中文回答，保持专业、简洁的语言风格。
    5. 如果用户的问题无法根据参考信息回答，请明确指出并给出建议。
"""

async def search_local_information_stream(query, workflow_params=None, user_id="", session_id="") -> AsyncGenerator[str, None]:
    """
    搜索本地信息并生成回答
    """
    # 检查是否是API函数调用请求
    if ENABLE_FUNCTION_CALLING:
        # 使用LangChain Agent处理查询
        print(f"尝试使用LangChain Agent处理查询: {query}")
        function_result = function_calling_agent(query, user_id=user_id, session_id=session_id)
        if function_result:
            # 如果有结果，直接返回
            print(f"LangChain Agent处理成功，返回结果")
            async def generate_function_result():
                yield function_result

            async for token in generate_function_result():
                yield token
            return
        else:
            print(f"LangChain Agent未返回结果，切换到向量检索方式")

    # 加载配置文件
    config = configparser.ConfigParser()
    # 尝试从当前目录、上级目录和项目根目录加载配置文件
    config_paths = [
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini"),  # src目录中
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.ini"),  # 项目根目录
        "config.ini"  # 当前工作目录
    ]

    config_loaded = False
    for config_path in config_paths:
        if os.path.exists(config_path):
            try:
                # 明确指定 UTF-8 编码
                config.read(config_path, encoding='utf-8')
                print(f"成功从 {config_path} 加载配置文件")
                config_loaded = True
                break
            except Exception as e:
                print(f"使用 UTF-8 编码从 {config_path} 加载配置文件失败: {str(e)}")
                try:
                    config.read(config_path, encoding='latin1')
                    print(f"成功从 {config_path} 使用latin1编码加载配置文件")
                    config_loaded = True
                    break
                except Exception as e2:
                    print(f"使用 latin1 编码从 {config_path} 加载配置文件失败: {str(e2)}")

    if not config_loaded:
        print("警告：未能从任何位置加载配置文件")
        yield "配置文件加载失败，请联系管理员"
        return

    # 创建提示模板
    prompt_template = PromptTemplate(
        template=template,
        input_variables=["context", "question"]
    )

    # 获取数据库路径
    persist_directory = config.get("DATABASE", "PERSIST_DIRECTORY", fallback="Private_GPT/db_DOC_basic")
    # 使用绝对路径
    persist_directory = os.path.abspath(persist_directory)
    print(f"使用向量数据库路径: {persist_directory}")

    # 获取向量数据库类型
    vector_db_type = "qdrant"  # 只使用qdrant
    print(f"使用向量数据库类型: {vector_db_type}")

    # 1. 初始化 callback handler 用于流式输出
    callback_handler = AsyncIteratorCallbackHandler()

    # 2. 配置 LLM
    llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")  # 获取LLM类型
    model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b")  # 获取模型名称

    # 初始化回调处理器
    callback_handler = AsyncIteratorCallbackHandler()

    # 根据配置选择LLM类型
    if llm_type.lower() == "openai":
        # 使用OpenAI API
        openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback="")
        openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback="https://api.openai.com/v1")

        print(f"使用OpenAI模型: {model_name}, API基础URL: {openai_api_base}")
        llm = ChatOpenAI(
            model_name=model_name,
            openai_api_key=openai_api_key,
            openai_api_base=openai_api_base,
            streaming=True,
            callbacks=[callback_handler]
        )
    else:
        # 默认使用Ollama
        ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")

        print(f"使用Ollama模型: {model_name}, 基础URL: {ollama_base_url}")
        llm = Ollama(
            model=model_name,
            base_url=ollama_base_url,
            callbacks=[callback_handler]
        )

    # 3. 设置检索器
    # 获取embedding类型和相关配置
    embedding_type = config.get("EMBEDDINGS", "EMBEDDING_TYPE", fallback="ollama")
    embeddings_model_name = config.get("EMBEDDINGS", "MODEL_NAME", fallback="Private_GPT/sentence-transformers/bge-large-zh-v1.5")
    base_url = config.get("EMBEDDINGS", "BASE_URL", fallback="http://localhost:11434")
    ollama_model = config.get("EMBEDDINGS", "OLLAMA_MODEL", fallback="bge-large:latest")

    # 根据配置选择embedding模型
    if embedding_type.lower() == "ollama":
        print(f"使用Ollama Embeddings模型: {ollama_model}, 基础URL: {base_url}")
        embeddings = OllamaEmbeddings(model=ollama_model, base_url=base_url)
    else:
        print(f"使用HuggingFace Embeddings模型: {embeddings_model_name}")
        embeddings = HuggingFaceEmbeddings(model_name=embeddings_model_name)

    # 根据配置选择向量数据库
    # 使用 Qdrant
    from langchain_community.vectorstores import Qdrant
    from qdrant_client import QdrantClient

    qdrant_host = config.get("QDRANT", "HOST", fallback="localhost")
    qdrant_port = config.getint("QDRANT", "PORT", fallback=7541)

    # 获取查询集合名称
    collection_name = config.get("QDRANT", "COLLECTION_NAME_QUERY", fallback="")
    if not collection_name:
        # 使用默认集合名称
        collection_name = "documents"

    print(f"连接 Qdrant 数据库: {qdrant_host}:{qdrant_port}")
    client = QdrantClient(host=qdrant_host, port=qdrant_port)

    print(f"使用 Qdrant 集合: {collection_name}")
    db = Qdrant(client=client, collection_name=collection_name, embeddings=embeddings)

    # 修改检索器配置，添加score_threshold
    retriever = db.as_retriever(
        search_kwargs={
            "k": target_source_chunks,
            "score_threshold": score_threshold
        }
    )

    # 4. 创建异步检索链
    qa = RetrievalQA.from_chain_type(
        llm=llm,
        chain_type="stuff",
        retriever=retriever,
        return_source_documents=True,
        chain_type_kwargs={
            "prompt": prompt_template,
            "document_variable_name": "context"
        }
    )

    # 5. 异步执行查询
    # # 处理历史对话 - 只保留最近的10轮对话，避免上下文混淆
    # chat_history_text = ""

    # # 兼容两种参数传递方式
    # chat_history = None
    # if workflow_params:
    #     if hasattr(workflow_params, 'chat_history'):
    #         chat_history = workflow_params.chat_history

    # if chat_history and len(chat_history) > 0:
    #     # 只使用最近的10轮对话作为上下文
    #     recent_history = chat_history[-10:]
    #     chat_history_text = "\n".join([f"用户: {item['user']}\n助手: {item['assistant']}" for item in recent_history])

    # 定义一些不需要检索文档的简单问候语
    greetings = ["你好", "您好", "早上好", "下午好", "晚上好", "嗨", "hi", "hello","你是谁"]

    # 如果是问候语，直接返回回应，不进行文档检索
    if query.lower().strip() in greetings:
        yield "你好！我是你的知识助手，有什么我可以帮你的吗？"
        return

    # 获取相关文档
    docs = retriever.get_relevant_documents(query)
    source_docs = ""
    for doc in docs:
        source_docs += f"{doc.page_content}\n"

    # 添加思考过程标记
    #yield "<think>正在分析问题并搜索相关文档...</think>"

    # 添加源文档标记 - 用于前端显示
    source_docs_display = ""
    for doc in docs:
        source_docs_display += f"> {doc.metadata['source']}:\n{doc.page_content}\n"
    yield f"<source>{source_docs_display}</source>"

    # 对于过长的查询，进行简化处理
    processed_query = query
    if len(query) > 20000:  # 如果查询超过2000个字符
        processed_query = query[:20000] + "..." # 只保留前500个字符用于检索

    # 执行查询：使用prompt_template
    task = asyncio.create_task(qa.ainvoke({
        "query": processed_query,
        "question": processed_query,
        "context": source_docs
    }))

    # 6. 流式输出结果
    try:
        await asyncio.sleep(0.1)
        async for token in callback_handler.aiter():
            yield token
    except Exception as e:
        print(f"Error during streaming: {e}")
        yield f"发生错误: {str(e)}"
    finally:
        callback_handler.done.set()

def parse_arguments():
    parser = argparse.ArgumentParser(description='privateGPT: Ask questions to your documents without an internet connection, '
                                                 'using the power of LLMs.')
    parser.add_argument("--hide-source", "-S", action='store_true',
                        help='Use this flag to disable printing of source documents used for answers.')

    parser.add_argument("--mute-stream", "-M",
                        action='store_true',
                        help='Use this flag to disable the streaming StdOut callback for LLMs.')

    return parser.parse_args()


async def stream_chat(query: str, user_id="", session_id="") -> AsyncGenerator[str, None]:
    """
    直接与大语言模型进行闲聊对话，不进行RAG检索
    """
    # 检查是否是API函数调用请求
    if ENABLE_FUNCTION_CALLING:
        # 使用LangChain Agent处理查询
        print(f"尝试使用LangChain Agent处理查询: {query}")
        function_result = function_calling_agent(query, user_id=user_id, session_id=session_id)
        if function_result:
            # 如果有结果，直接返回
            print(f"LangChain Agent处理成功，返回结果")
            async def generate_function_result():
                yield function_result

            async for token in generate_function_result():
                yield token
            return
        else:
            print(f"LangChain Agent未返回结果，切换到向量检索方式")

    try:
        print(f"开始闲聊模式，用户查询: {query}")
        # 定义一些不需要检索文档的简单问候语
        greetings = ["你好", "您好", "早上好", "下午好", "晚上好", "嗨", "hi", "hello","你是谁"]

        # 如果是问候语，直接返回回应，不进行文档检索
        if query.lower().strip() in greetings:
            yield "你好！我是你的知识助手，有什么我可以帮你的吗？"
            return
    
        # 创建异步回调处理器
        callback = AsyncIteratorCallbackHandler()

        # 根据配置选择LLM
        llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")
        model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b")

        if llm_type.lower() == "openai":
            # 使用OpenAI模型
            openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback="")
            openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback="https://api.openai.com/v1")

            llm = ChatOpenAI(
                model_name=model_name,
                openai_api_key=openai_api_key,
                openai_api_base=openai_api_base,
                streaming=True,
                callbacks=[callback],
                temperature=0.7,  # 闲聊模式可以稍微提高创造性
            )
        else:
            # 使用Ollama模型
            ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")

            llm = Ollama(
                model=model_name,
                base_url=ollama_base_url,
                callbacks=[callback],
                temperature=0.7,  # 闲聊模式可以稍微提高创造性
            )

        # 创建闲聊提示词模板
        chat_template = """你是一个友好、有帮助的AI助手。请根据用户的问题进行自然、有趣的对话。

用户问题: {question}

请提供一个友好、有帮助的回答："""

        prompt = PromptTemplate(
            template=chat_template,
            input_variables=["question"]
        )

        # 格式化提示词
        formatted_prompt = prompt.format(question=query)

        # 创建异步任务来调用LLM
        task = asyncio.create_task(
            llm.ainvoke(formatted_prompt)
        )

        # 流式输出响应
        async for token in callback.aiter():
            yield token

        # 等待任务完成
        await task

        print("闲聊模式响应完成")

    except Exception as e:
        error_msg = f"闲聊模式处理失败: {str(e)}"
        print(error_msg)
        yield error_msg


# if __name__ == "__main__":