# 新功能使用指南

## 概述

本次更新实现了两个主要功能：
1. **API工具配置管理端点模块化** - 将API工具配置相关端点独立为单独模块
2. **闲聊模式支持** - 添加了不使用RAG的直接对话功能

## 1. API工具配置管理端点模块化

### 🔧 实现内容

**新增文件**：
- `src/app/api_tools_endpoints.py` - API工具配置管理的所有端点
- 更新了 `src/app/__init__.py` - 导出新的路由器

**主要改进**：
- ✅ 代码结构更清晰，职责分离
- ✅ 便于维护和扩展
- ✅ 遵循FastAPI最佳实践
- ✅ 支持依赖注入和错误处理

### 📋 端点列表

| 方法 | 路径 | 功能 |
|------|------|------|
| GET | `/api/tools` | 获取所有工具配置 |
| GET | `/api/tools/{tool_id}` | 获取单个工具配置 |
| POST | `/api/tools` | 创建工具配置 |
| PUT | `/api/tools/{tool_id}` | 更新工具配置 |
| DELETE | `/api/tools/{tool_id}` | 删除工具配置 |
| GET | `/api/tools/export` | 导出工具配置 |
| POST | `/api/tools/import` | 导入工具配置 |
| POST | `/api/tools/refresh` | 刷新工具缓存 |
| GET | `/tools-config` | 配置页面 |

### 🔄 集成方式

在 `main.py` 中通过以下方式集成：

```python
from src.app import api_tools_router, page_router

# 添加API工具配置路由
app.include_router(api_tools_router)
app.include_router(page_router)
```

## 2. 闲聊模式支持

### 🎯 功能特点

- **直接对话**：不进行RAG检索，直接与大语言模型交互
- **流式输出**：支持实时流式响应
- **配置灵活**：支持Ollama和OpenAI两种LLM
- **向后兼容**：不影响现有RAG功能

### 📝 实现细节

**新增函数**：
- `src/privateGPT_res.py` 中的 `stream_chat()` 函数

**核心特性**：
```python
async def stream_chat(query: str) -> AsyncGenerator[str, None]:
    """
    直接与大语言模型进行闲聊对话，不进行RAG检索
    """
    # 支持Ollama和OpenAI两种LLM
    # 使用异步流式输出
    # 提高temperature增加创造性
```

### 🔧 参数配置

在 `/run_workflow/` 端点中添加了 `enable_rag` 参数：

```python
# 请求格式
{
    "query": "用户问题",
    "enable_rag": true/false  # 可选，默认true
}
```

### 📊 使用示例

#### RAG模式（默认）
```json
POST /run_workflow/
{
    "query": "什么是应急管理？",
    "enable_rag": true
}
```
- 会检索向量数据库
- 基于知识库回答
- 支持函数调用

#### 闲聊模式
```json
POST /run_workflow/
{
    "query": "你好，请介绍一下自己",
    "enable_rag": false
}
```
- 不检索向量数据库
- 直接与LLM对话
- 更加自然和创造性

#### 兼容模式
```json
POST /run_workflow/
{
    "query": "问题内容"
}
```
- 不传 `enable_rag` 参数
- 默认启用RAG模式
- 保持向后兼容

## 3. 技术实现

### 🏗️ 架构改进

**模块化结构**：
```
src/app/
├── __init__.py              # 模块导出
├── auth.py                  # 认证相关
├── user_routes.py          # 用户路由
└── api_tools_endpoints.py  # API工具配置端点 (新增)
```

**流程优化**：
```
用户请求 → main.py:/run_workflow/
    ↓
解析参数 (query, enable_rag)
    ↓
enable_rag判断
    ├─ true  → search_local_information_stream (RAG模式)
    └─ false → stream_chat (闲聊模式)
    ↓
流式返回结果
```

### 🔍 关键改进

1. **智能模式切换**：
   - RAG模式：检查Qdrant连接，进行向量检索
   - 闲聊模式：跳过数据库检查，直接调用LLM

2. **错误处理优化**：
   - 闲聊模式下不会因为Qdrant连接失败而报错
   - 独立的错误处理机制

3. **性能优化**：
   - 闲聊模式避免不必要的数据库连接
   - 流式输出减少延迟

## 4. 配置说明

### LLM配置
```ini
[LLM]
LLM_TYPE = ollama  # 或 openai
MODEL = deepseek-r1:32b
OLLAMA_BASE_URL = http://localhost:11434
# 或者
OPENAI_API_KEY = your_key
OPENAI_API_BASE = https://api.openai.com/v1
```

### 闲聊模式特殊配置
- `temperature = 0.7` - 提高创造性
- `streaming = True` - 启用流式输出
- 使用友好的对话提示词模板

## 5. 使用场景

### RAG模式适用场景
- 专业知识问答
- 基于文档的查询
- 需要准确性的问题
- 函数调用和API操作

### 闲聊模式适用场景
- 日常对话
- 创意写作
- 开放性问题
- 娱乐性交互

## 6. 兼容性说明

### ✅ 完全兼容
- 现有的API调用方式不变
- 前端无需修改即可使用
- 所有现有功能正常工作

### 🆕 新增功能
- 前端可以通过 `enable_rag` 参数控制模式
- 支持动态切换RAG和闲聊模式
- API工具配置管理更加模块化

## 7. 部署说明

### 无需额外配置
- 所有新功能都是可选的
- 默认行为与之前完全一致
- 渐进式升级，风险最小

### 推荐配置
1. 确保LLM服务正常运行
2. 根据需要配置Ollama或OpenAI
3. 测试闲聊模式功能
4. 根据使用场景调整参数

## 8. 监控和日志

### 日志增强
- 记录模式选择：`RAG模式: 启用/禁用`
- 区分不同模式的处理流程
- 独立的错误处理和记录

### 性能监控
- 闲聊模式响应时间
- RAG模式检索效率
- 模式切换统计

这些新功能大大增强了系统的灵活性和用户体验，同时保持了完全的向后兼容性。用户可以根据不同的使用场景选择最适合的交互模式。
